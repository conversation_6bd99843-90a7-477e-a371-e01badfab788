# STM32F4电机驱动测试项目需求文档 (PRD)

## 1. 文档信息
- **版本**: v1.0
- **创建日期**: 2025-01-15
- **负责人**: Emma (产品经理)
- **项目名称**: STM32F4双电机驱动测试程序

## 2. 背景与问题陈述

### 2.1 项目背景
基于STM32F4微控制器和TB6612电机驱动芯片，开发一个简单的双电机驱动测试程序。该项目旨在验证硬件连接的正确性和基础电机驱动功能。

### 2.2 问题陈述
- 需要验证STM32F4与TB6612电机驱动芯片的硬件连接是否正确
- 需要测试双电机的基础驱动功能
- 需要一个简单的上电即可运行的测试程序

## 3. 目标与成功指标

### 3.1 项目目标
- **主要目标**: 开发一个上电即可驱动双电机转动的测试程序
- **次要目标**: 为后续复杂电机控制功能奠定基础

### 3.2 关键结果 (Key Results)
- 上电后双电机能够同时开始转动
- 电机转动方向可控制
- 电机转速可通过PWM调节
- 代码结构清晰，易于理解和扩展

### 3.3 成功指标
- 双电机能够稳定转动
- PWM信号输出正常
- GPIO控制信号正确
- 无硬件损坏或异常

## 4. 硬件配置规格

### 4.1 引脚分配
**左电机 (电机A)**:
- PE12 (AIN1): 电机A方向控制1
- PE14 (AIN2): 电机A方向控制2  
- PE9 (TIM1_CH1): 电机A速度控制PWM

**右电机 (电机B)**:
- PE8 (BIN1): 电机B方向控制1
- PE10 (BIN2): 电机B方向控制2
- PE11 (TIM1_CH2): 电机B速度控制PWM

**控制信号**:
- 3.3V -> TB6612 VCC: 逻辑电源
- 3.3V -> TB6612 STBY: 待机控制 (高电平使能)
- GND -> TB6612 GND: 共地连接

### 4.2 定时器配置
- **TIM1**: PWM模式，预分频器8-1，周期1000-1
- **PWM频率**: 约21kHz (168MHz/8/1000)
- **PWM通道**: CH1(PE9), CH2(PE11)

## 5. 功能规格详述

### 5.1 核心功能
1. **电机初始化**: 配置PWM和GPIO
2. **电机启动**: 上电后自动开始转动
3. **方向控制**: 设置电机正转方向
4. **速度控制**: 通过PWM占空比控制转速

### 5.2 电机控制逻辑
**TB6612真值表**:
- AIN1=1, AIN2=0: 电机A正转
- AIN1=0, AIN2=1: 电机A反转  
- AIN1=0, AIN2=0: 电机A停止
- AIN1=1, AIN2=1: 电机A刹车

### 5.3 测试场景
- **场景1**: 上电后双电机同时正转
- **场景2**: 可调节转速 (PWM占空比50%)
- **场景3**: 持续运行测试

## 6. 范围定义

### 6.1 包含功能 (In Scope)
- 基础PWM输出控制
- GPIO方向控制
- 双电机同步驱动
- 上电自动启动
- 基础安全保护 (STBY控制)

### 6.2 排除功能 (Out of Scope)
- 编码器反馈功能
- 闭环速度控制
- 复杂运动控制算法
- 串口通信控制
- 用户交互界面

## 7. 技术约束与依赖

### 7.1 技术约束
- 基于STM32F4平台
- 使用STM32 HAL库
- 代码必须放在USER CODE区域
- 遵循STM32CubeMX项目结构

### 7.2 依赖项
- STM32CubeMX生成的基础项目
- HAL库TIM和GPIO模块
- 正确的硬件连接

## 8. 风险评估

### 8.1 技术风险
- **硬件连接错误**: 可能导致电机无法转动或损坏
- **PWM配置错误**: 可能导致电机转速异常
- **电源问题**: 可能导致系统不稳定

### 8.2 缓解措施
- 仔细检查硬件连接
- 使用适当的PWM频率和占空比
- 添加STBY控制确保安全启动

## 9. 验收标准

### 9.1 功能验收
- [ ] 上电后双电机能够自动开始转动
- [ ] 电机转动方向正确 (正转)
- [ ] 电机转速稳定
- [ ] PWM信号输出正常

### 9.2 代码质量验收
- [ ] 代码结构清晰，注释完整
- [ ] 遵循STM32 HAL库编程规范
- [ ] 无编译错误和警告
- [ ] 代码放置在正确的USER CODE区域

## 10. 交付物清单

1. **源代码**: 修改后的main.c文件
2. **技术文档**: 代码实现说明
3. **测试报告**: 功能验证结果
4. **使用说明**: 编译和运行指南