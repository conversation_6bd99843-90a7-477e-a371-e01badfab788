#include "mpu6050.h"
#include <stdio.h>

/**
 * @brief 向MPU6050写入单个字节
 * @param reg 寄存器地址
 * @param data 要写入的数据
 * @return HAL状态
 */
HAL_StatusTypeDef MPU6050_WriteByte(uint8_t reg, uint8_t data) {
    return HAL_I2C_Mem_Write(&hi2c1, MPU6050_ADDRESS, reg, 
                            I2C_MEMADD_SIZE_8BIT, &data, 1, HAL_MAX_DELAY);
}

/**
 * @brief 从MPU6050读取单个字节
 * @param reg 寄存器地址
 * @param data 读取数据的指针
 * @return HAL状态
 */
HAL_StatusTypeDef MPU6050_ReadByte(uint8_t reg, uint8_t *data) {
    return HAL_I2C_Mem_Read(&hi2c1, MPU6050_ADDRESS, reg, 
                           I2C_MEMADD_SIZE_8BIT, data, 1, HAL_MAX_DELAY);
}

/**
 * @brief 从MPU6050读取多个字节
 * @param reg 起始寄存器地址
 * @param length 读取字节数
 * @param data 读取数据的缓冲区
 * @return HAL状态
 */
HAL_StatusTypeDef MPU6050_ReadBytes(uint8_t reg, uint8_t length, uint8_t *data) {
    return HAL_I2C_Mem_Read(&hi2c1, MPU6050_ADDRESS, reg, 
                           I2C_MEMADD_SIZE_8BIT, data, length, HAL_MAX_DELAY);
}

/**
 * @brief 测试MPU6050连接
 * @return 1-连接成功, 0-连接失败
 */
uint8_t MPU6050_Test_Connection(void) {
    uint8_t data;
    if(MPU6050_ReadByte(MPU6050_WHO_AM_I, &data) == HAL_OK) {
        return (data == 0x68) ? 1 : 0;
    }
    return 0;
}

/**
 * @brief MPU6050初始化
 * @return HAL状态
 */
HAL_StatusTypeDef MPU6050_Init(void) {
    uint8_t data;
    
    printf("正在初始化MPU6050...\r\n");
    
    // 检测设备连接
    if(!MPU6050_Test_Connection()) {
        printf("❌ MPU6050连接失败！请检查硬件连接\r\n");
        printf("检查项目:\r\n");
        printf("1. VCC -> 3.3V\r\n");
        printf("2. GND -> GND\r\n");
        printf("3. SCL -> PB6\r\n");
        printf("4. SDA -> PB7\r\n");
        return HAL_ERROR;
    }
    
    printf("✅ MPU6050设备检测成功 (ID: 0x68)\r\n");
    
    // 复位设备
    if(MPU6050_WriteByte(MPU6050_PWR_MGMT_1, 0x80) != HAL_OK) {
        printf("❌ MPU6050复位失败\r\n");
        return HAL_ERROR;
    }
    HAL_Delay(100);  // 等待复位完成
    
    // 配置电源管理 - 选择X轴陀螺仪作为时钟源，退出睡眠模式
    if(MPU6050_WriteByte(MPU6050_PWR_MGMT_1, 0x01) != HAL_OK) {
        printf("❌ 电源管理配置失败\r\n");
        return HAL_ERROR;
    }
    
    // 配置陀螺仪量程 ±2000°/s (最高精度)
    if(MPU6050_WriteByte(MPU6050_GYRO_CONFIG, 0x18) != HAL_OK) {
        printf("❌ 陀螺仪配置失败\r\n");
        return HAL_ERROR;
    }
    
    // 配置加速度计量程 ±2g (最高精度)
    if(MPU6050_WriteByte(MPU6050_ACCEL_CONFIG, 0x00) != HAL_OK) {
        printf("❌ 加速度计配置失败\r\n");
        return HAL_ERROR;
    }
    
    // 配置低通滤波器 44Hz (平衡噪声和响应速度)
    if(MPU6050_WriteByte(MPU6050_CONFIG, 0x03) != HAL_OK) {
        printf("❌ 滤波器配置失败\r\n");
        return HAL_ERROR;
    }
    
    // 配置采样率 1kHz (1000Hz / (0+1) = 1000Hz)
    if(MPU6050_WriteByte(MPU6050_SMPLRT_DIV, 0x00) != HAL_OK) {
        printf("❌ 采样率配置失败\r\n");
        return HAL_ERROR;
    }
    
    // 验证配置
    MPU6050_ReadByte(MPU6050_PWR_MGMT_1, &data);
    printf("电源管理寄存器: 0x%02X\r\n", data);
    
    MPU6050_ReadByte(MPU6050_GYRO_CONFIG, &data);
    printf("陀螺仪配置: 0x%02X (±2000°/s)\r\n", data);
    
    MPU6050_ReadByte(MPU6050_ACCEL_CONFIG, &data);
    printf("加速度计配置: 0x%02X (±2g)\r\n", data);
    
    printf("🎉 MPU6050初始化完成！\r\n");
    printf("传感器规格:\r\n");
    printf("- 陀螺仪: ±2000°/s (16.4 LSB/°/s)\r\n");
    printf("- 加速度计: ±2g (16384 LSB/g)\r\n");
    printf("- 采样率: 1000Hz\r\n");
    printf("- 滤波器: 44Hz低通\r\n");
    
    return HAL_OK;
}

/**
 * @brief 读取MPU6050所有传感器数据
 * @param data 数据结构指针
 * @return HAL状态
 */
HAL_StatusTypeDef MPU6050_ReadData(MPU6050_Data *data) {
    uint8_t buf[14];
    
    // 一次性读取14个字节 (加速度6字节 + 温度2字节 + 陀螺仪6字节)
    if(MPU6050_ReadBytes(MPU6050_ACCEL_XOUT_H, 14, buf) != HAL_OK) {
        return HAL_ERROR;
    }
    
    // 解析数据 (大端序转小端序)
    data->Accel_X = (int16_t)(buf[0] << 8 | buf[1]);
    data->Accel_Y = (int16_t)(buf[2] << 8 | buf[3]);
    data->Accel_Z = (int16_t)(buf[4] << 8 | buf[5]);
    data->Temp = (int16_t)(buf[6] << 8 | buf[7]);
    data->Gyro_X = (int16_t)(buf[8] << 8 | buf[9]);
    data->Gyro_Y = (int16_t)(buf[10] << 8 | buf[11]);
    data->Gyro_Z = (int16_t)(buf[12] << 8 | buf[13]);
    
    return HAL_OK;
}
