# STM32F4电机驱动系统架构设计文档

## 1. 文档信息
- **版本**: v1.0
- **创建日期**: 2025-01-15
- **负责人**: Bob (系统架构师)
- **项目名称**: STM32F4双电机驱动测试系统

## 2. 架构概述

### 2.1 系统架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    STM32F4 Application Layer                │
├─────────────────────────────────────────────────────────────┤
│  Motor Control Module                                       │
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │   Motor A       │  │   Motor B       │                  │
│  │   Controller    │  │   Controller    │                  │
│  └─────────────────┘  └─────────────────┘                  │
├─────────────────────────────────────────────────────────────┤
│                    HAL Abstraction Layer                    │
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │   PWM Module    │  │   GPIO Module   │                  │
│  │   (TIM1)        │  │   (GPIOE)       │                  │
│  └─────────────────┘  └─────────────────┘                  │
├─────────────────────────────────────────────────────────────┤
│                    Hardware Layer                           │
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │   STM32F407     │  │   TB6612FNG     │                  │
│  │   MCU           │  │   Motor Driver  │                  │
│  └─────────────────┘  └─────────────────┘                  │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 核心设计原则
1. **模块化设计**: 电机控制功能独立封装
2. **HAL库抽象**: 使用STM32 HAL库提供的硬件抽象
3. **可扩展性**: 为后续功能扩展预留接口
4. **安全性**: 包含必要的安全保护机制

## 3. 硬件架构设计

### 3.1 硬件连接架构
```
STM32F407VGT6                    TB6612FNG
┌─────────────────┐             ┌─────────────────┐
│                 │             │                 │
│ PE9  (TIM1_CH1) ├─────────────┤ PWMA            │
│ PE11 (TIM1_CH2) ├─────────────┤ PWMB            │
│                 │             │                 │
│ PE12 (AIN1)     ├─────────────┤ AIN1            │
│ PE14 (AIN2)     ├─────────────┤ AIN2            │
│ PE8  (BIN1)     ├─────────────┤ BIN1            │
│ PE10 (BIN2)     ├─────────────┤ BIN2            │
│                 │             │                 │
│ 3.3V            ├─────────────┤ VCC             │
│ 3.3V            ├─────────────┤ STBY            │
│ GND             ├─────────────┤ GND             │
└─────────────────┘             └─────────────────┘
```

### 3.2 时钟和定时器配置
- **系统时钟**: 168MHz (HSE + PLL)
- **APB2时钟**: 84MHz (TIM1时钟源)
- **TIM1配置**:
  - 预分频器: 8-1 (21MHz计数频率)
  - 自动重载值: 1000-1
  - PWM频率: 21kHz
  - PWM分辨率: 1000级 (0.1%精度)

### 3.3 GPIO配置架构
```
GPIOE端口配置:
┌──────┬─────────┬──────────┬─────────────┐
│ 引脚 │ 功能    │ 模式     │ 速度        │
├──────┼─────────┼──────────┼─────────────┤
│ PE8  │ BIN1    │ 输出推挽 │ 低速        │
│ PE9  │ PWM_A   │ 复用功能 │ 高速        │
│ PE10 │ BIN2    │ 输出推挽 │ 低速        │
│ PE11 │ PWM_B   │ 复用功能 │ 高速        │
│ PE12 │ AIN1    │ 输出推挽 │ 低速        │
│ PE14 │ AIN2    │ 输出推挽 │ 低速        │
└──────┴─────────┴──────────┴─────────────┘
```

## 4. 软件架构设计

### 4.1 分层架构
```
Application Layer (应用层)
├── Motor Control Logic
├── Safety Management
└── System Initialization

HAL Layer (硬件抽象层)
├── TIM HAL Driver
├── GPIO HAL Driver
└── System HAL Driver

Hardware Layer (硬件层)
├── STM32F407 MCU
└── TB6612 Motor Driver
```

### 4.2 模块设计

#### 4.2.1 电机控制模块
```c
// 电机控制结构体
typedef struct {
    TIM_HandleTypeDef* pwm_timer;    // PWM定时器句柄
    uint32_t pwm_channel;            // PWM通道
    GPIO_TypeDef* dir_port1;         // 方向控制端口1
    uint16_t dir_pin1;               // 方向控制引脚1
    GPIO_TypeDef* dir_port2;         // 方向控制端口2
    uint16_t dir_pin2;               // 方向控制引脚2
    uint16_t speed;                  // 当前速度值
    uint8_t direction;               // 当前方向
} Motor_TypeDef;

// 电机方向枚举
typedef enum {
    MOTOR_STOP = 0,
    MOTOR_FORWARD,
    MOTOR_BACKWARD,
    MOTOR_BRAKE
} Motor_Direction_t;
```

#### 4.2.2 核心函数接口
```c
// 电机初始化
HAL_StatusTypeDef Motor_Init(Motor_TypeDef* motor);

// 电机启动
HAL_StatusTypeDef Motor_Start(Motor_TypeDef* motor);

// 电机停止
HAL_StatusTypeDef Motor_Stop(Motor_TypeDef* motor);

// 设置电机方向
HAL_StatusTypeDef Motor_SetDirection(Motor_TypeDef* motor, Motor_Direction_t dir);

// 设置电机速度
HAL_StatusTypeDef Motor_SetSpeed(Motor_TypeDef* motor, uint16_t speed);
```

### 4.3 数据流架构
```
Main Function
     │
     ├── System_Init()
     │   ├── HAL_Init()
     │   ├── SystemClock_Config()
     │   ├── MX_GPIO_Init()
     │   └── MX_TIM1_Init()
     │
     ├── Motor_Init()
     │   ├── Motor_A_Init()
     │   └── Motor_B_Init()
     │
     ├── Motor_Start()
     │   ├── PWM_Start()
     │   ├── Set_Direction()
     │   └── Set_Speed()
     │
     └── Main_Loop()
         └── Motor_Control_Task()
```

## 5. 安全架构设计

### 5.1 安全保护机制
1. **STBY控制**: 确保TB6612正常启用
2. **PWM限制**: 限制最大PWM占空比防止过载
3. **初始化检查**: 验证硬件初始化状态
4. **错误处理**: HAL函数返回值检查

### 5.2 故障处理架构
```c
typedef enum {
    MOTOR_OK = 0,
    MOTOR_ERROR_INIT,
    MOTOR_ERROR_PWM,
    MOTOR_ERROR_GPIO,
    MOTOR_ERROR_TIMEOUT
} Motor_Status_t;

// 错误处理函数
void Motor_ErrorHandler(Motor_Status_t error);
```

## 6. 性能架构设计

### 6.1 性能指标
- **PWM频率**: 21kHz (超声波频率，减少噪音)
- **PWM分辨率**: 1000级 (0.1%精度)
- **响应时间**: <1ms (PWM更新周期)
- **CPU占用率**: <5% (简单控制逻辑)

### 6.2 资源使用
- **Flash使用**: <10KB (基础功能)
- **RAM使用**: <1KB (变量和堆栈)
- **定时器资源**: TIM1 (2个PWM通道)
- **GPIO资源**: 6个输出引脚

## 7. 扩展架构设计

### 7.1 预留扩展接口
1. **编码器接口**: TIM3/TIM4已配置为编码器模式
2. **串口通信**: USART1/USART2可用于控制命令
3. **闭环控制**: 预留PID控制算法接口
4. **多电机支持**: 架构支持扩展更多电机

### 7.2 未来架构演进
```
Current: Basic Motor Control
    ↓
Phase 1: Encoder Feedback
    ↓
Phase 2: Closed-loop Control
    ↓
Phase 3: Advanced Motion Control
```

## 8. 实现约束

### 8.1 技术约束
- 必须使用STM32 HAL库
- 代码放置在USER CODE区域
- 遵循STM32CubeMX项目结构
- 兼容MDK-ARM编译环境

### 8.2 性能约束
- 实时性要求: 软实时系统
- 内存限制: 1MB Flash, 192KB RAM
- 功耗要求: 正常工作模式
- 温度范围: -40°C to +85°C

## 9. 架构决策记录 (ADR)

### ADR-001: 使用HAL库而非寄存器操作
**决策**: 使用STM32 HAL库进行硬件操作
**理由**: 提高代码可读性和可维护性，减少开发时间
**影响**: 略微增加代码大小，但提高开发效率

### ADR-002: PWM频率选择21kHz
**决策**: 设置PWM频率为21kHz
**理由**: 超声波频率减少噪音，适合电机驱动
**影响**: 良好的电机控制性能和用户体验

### ADR-003: 模块化电机控制设计
**决策**: 将电机控制封装为独立模块
**理由**: 便于代码复用和后续功能扩展
**影响**: 增加代码结构复杂度，但提高可维护性

## 10. 架构验证

### 10.1 架构验证标准
- [ ] 硬件连接正确性验证
- [ ] PWM信号输出验证
- [ ] GPIO控制信号验证
- [ ] 电机转动功能验证
- [ ] 安全保护机制验证

### 10.2 性能验证
- [ ] PWM频率测量: 21kHz ±1%
- [ ] PWM占空比精度: ±0.1%
- [ ] 系统响应时间: <1ms
- [ ] CPU占用率: <5%