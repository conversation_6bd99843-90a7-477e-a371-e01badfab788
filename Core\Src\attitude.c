#include "attitude.h"
#include <stdio.h>

// 静态变量
static KalmanFilter pitch_kalman;  // 俯仰角卡尔曼滤波器
static KalmanFilter roll_kalman;   // 横滚角卡尔曼滤波器
static float gyro_offset_x = 0;    // 陀螺仪X轴零点偏移
static float gyro_offset_y = 0;    // 陀螺仪Y轴零点偏移
static float gyro_offset_z = 0;    // 陀螺仪Z轴零点偏移

/**
 * @brief 互补滤波算法
 * @param accelAngle 加速度计计算的角度
 * @param gyroRate 陀螺仪角速度
 * @param dt 采样时间间隔
 * @param alpha 滤波系数 (0-1, 越大越信任陀螺仪)
 * @return 滤波后的角度
 */
float ComplementaryFilter(float accelAngle, float gyroRate, float dt, float alpha) {
    static float angle = 0;
    // 互补滤波公式: angle = α*(angle + gyro*dt) + (1-α)*accel_angle
    angle = alpha * (angle + gyroRate * dt) + (1 - alpha) * accelAngle;
    return angle;
}

/**
 * @brief 卡尔曼滤波器初始化
 * @param kf 卡尔曼滤波器指针
 */
void Kalman_Init(KalmanFilter *kf) {
    kf->Q_angle = 0.001f;    // 角度过程噪声
    kf->Q_bias = 0.003f;     // 偏置过程噪声
    kf->R_measure = 0.03f;   // 测量噪声
    kf->angle = 0.0f;        // 初始角度
    kf->bias = 0.0f;         // 初始偏置
    
    // 初始化协方差矩阵
    kf->P[0][0] = 0.0f;
    kf->P[0][1] = 0.0f;
    kf->P[1][0] = 0.0f;
    kf->P[1][1] = 0.0f;
}

/**
 * @brief 卡尔曼滤波更新
 * @param kf 卡尔曼滤波器指针
 * @param newAngle 新的角度测量值 (加速度计)
 * @param newRate 新的角速度测量值 (陀螺仪)
 * @param dt 时间间隔
 * @return 滤波后的最优角度估计
 */
float Kalman_Update(KalmanFilter *kf, float newAngle, float newRate, float dt) {
    // 预测步骤
    kf->angle += dt * (newRate - kf->bias);  // 预测角度
    kf->P[0][0] += dt * (dt*kf->P[1][1] - kf->P[0][1] - kf->P[1][0] + kf->Q_angle);
    kf->P[0][1] -= dt * kf->P[1][1];
    kf->P[1][0] -= dt * kf->P[1][1];
    kf->P[1][1] += kf->Q_bias * dt;

    // 更新步骤
    float y = newAngle - kf->angle;  // 测量残差
    float S = kf->P[0][0] + kf->R_measure;  // 残差协方差
    float K[2];  // 卡尔曼增益
    K[0] = kf->P[0][0] / S;
    K[1] = kf->P[1][0] / S;

    // 更新估计值
    kf->angle += K[0] * y;
    kf->bias += K[1] * y;

    // 更新协方差矩阵
    float P00_temp = kf->P[0][0];
    float P01_temp = kf->P[0][1];

    kf->P[0][0] -= K[0] * P00_temp;
    kf->P[0][1] -= K[0] * P01_temp;
    kf->P[1][0] -= K[1] * P00_temp;
    kf->P[1][1] -= K[1] * P01_temp;

    return kf->angle;
}

/**
 * @brief 计算加速度计X轴角度 (俯仰角)
 * @param data MPU6050数据
 * @return X轴角度 (度)
 */
float Calculate_Accel_Angle_X(MPU6050_Data *data) {
    // 使用atan2计算俯仰角: pitch = atan2(accel_y, accel_z)
    return atan2f(data->Accel_Y, data->Accel_Z) * RAD_TO_DEG;
}

/**
 * @brief 计算加速度计Y轴角度 (横滚角)
 * @param data MPU6050数据
 * @return Y轴角度 (度)
 */
float Calculate_Accel_Angle_Y(MPU6050_Data *data) {
    // 使用atan2计算横滚角: roll = atan2(-accel_x, sqrt(accel_y^2 + accel_z^2))
    float accel_magnitude = sqrtf(data->Accel_Y * data->Accel_Y + data->Accel_Z * data->Accel_Z);
    return atan2f(-data->Accel_X, accel_magnitude) * RAD_TO_DEG;
}

/**
 * @brief 姿态解算初始化
 */
void Attitude_Init(void) {
    printf("正在初始化姿态解算模块...\r\n");
    
    // 初始化卡尔曼滤波器
    Kalman_Init(&pitch_kalman);
    Kalman_Init(&roll_kalman);
    
    printf("✅ 姿态解算模块初始化完成\r\n");
    printf("滤波算法: 互补滤波 (α=%.2f)\r\n", COMPLEMENTARY_ALPHA);
    printf("采样频率: %.0fHz (%.1fms)\r\n", 1.0f/SAMPLE_TIME, SAMPLE_TIME*1000);
}

/**
 * @brief 陀螺仪零点校准
 */
void Attitude_Calibrate(void) {
    printf("开始陀螺仪零点校准...\r\n");
    printf("请保持传感器静止不动！\r\n");
    
    const int calibration_samples = 1000;  // 校准样本数
    float sum_x = 0, sum_y = 0, sum_z = 0;
    MPU6050_Data data;
    
    for(int i = 0; i < calibration_samples; i++) {
        if(MPU6050_ReadData(&data) == HAL_OK) {
            sum_x += data.Gyro_X;
            sum_y += data.Gyro_Y;
            sum_z += data.Gyro_Z;
        }
        HAL_Delay(2);  // 2ms间隔
        
        // 每100次显示进度
        if((i + 1) % 100 == 0) {
            printf("校准进度: %d/%d\r\n", i + 1, calibration_samples);
        }
    }
    
    // 计算平均值作为零点偏移
    gyro_offset_x = sum_x / calibration_samples;
    gyro_offset_y = sum_y / calibration_samples;
    gyro_offset_z = sum_z / calibration_samples;
    
    printf("✅ 陀螺仪校准完成！\r\n");
    printf("零点偏移: X=%.1f, Y=%.1f, Z=%.1f\r\n", 
           gyro_offset_x, gyro_offset_y, gyro_offset_z);
}

/**
 * @brief 更新姿态数据
 * @param imu_data MPU6050原始数据
 * @param attitude 姿态数据输出
 * @return HAL状态
 */
HAL_StatusTypeDef Attitude_Update(MPU6050_Data *imu_data, Attitude_Data *attitude) {
    // 计算加速度计角度
    attitude->accel_angle_x = Calculate_Accel_Angle_X(imu_data);
    attitude->accel_angle_y = Calculate_Accel_Angle_Y(imu_data);
    
    // 计算陀螺仪角速度 (去除零点偏移，转换为度/秒)
    attitude->gyro_rate_x = (imu_data->Gyro_X - gyro_offset_x) / GYRO_SCALE;
    attitude->gyro_rate_y = (imu_data->Gyro_Y - gyro_offset_y) / GYRO_SCALE;
    attitude->gyro_rate_z = (imu_data->Gyro_Z - gyro_offset_z) / GYRO_SCALE;
    
    // 使用互补滤波计算最终角度
    attitude->pitch = ComplementaryFilter(attitude->accel_angle_x, 
                                         attitude->gyro_rate_x, 
                                         SAMPLE_TIME, 
                                         COMPLEMENTARY_ALPHA);
    
    attitude->roll = ComplementaryFilter(attitude->accel_angle_y, 
                                        attitude->gyro_rate_y, 
                                        SAMPLE_TIME, 
                                        COMPLEMENTARY_ALPHA);
    
    // 偏航角积分 (简单积分，会有累积误差)
    attitude->yaw += attitude->gyro_rate_z * SAMPLE_TIME;
    
    return HAL_OK;
}
