#ifndef __ATTITUDE_H
#define __ATTITUDE_H

#include "main.h"
#include "mpu6050.h"
#include <math.h>

// 数学常量
#define PI 3.14159265359f
#define RAD_TO_DEG 57.2957795131f
#define DEG_TO_RAD 0.0174532925f

// 滤波参数
#define COMPLEMENTARY_ALPHA 0.98f  // 互补滤波系数 (0.98偏重陀螺仪，0.02偏重加速度计)
#define SAMPLE_TIME 0.005f         // 5ms采样周期 (200Hz)

// 传感器量程转换系数
#define GYRO_SCALE 16.4f          // ±2000°/s -> 16.4 LSB/°/s
#define ACCEL_SCALE 16384.0f      // ±2g -> 16384 LSB/g

// 卡尔曼滤波器结构
typedef struct {
    float Q_angle;   // 过程噪声协方差 (角度)
    float Q_bias;    // 过程噪声协方差 (陀螺仪偏置)
    float R_measure; // 测量噪声协方差 (加速度计)
    float angle;     // 计算得到的最优角度
    float bias;      // 陀螺仪偏置
    float P[2][2];   // 误差协方差矩阵
} KalmanFilter;

// 姿态数据结构
typedef struct {
    float pitch;     // 俯仰角 (前后倾斜)
    float roll;      // 横滚角 (左右倾斜)
    float yaw;       // 偏航角 (旋转)
    float accel_angle_x; // 加速度计计算的X轴角度
    float accel_angle_y; // 加速度计计算的Y轴角度
    float gyro_rate_x;   // 陀螺仪X轴角速度
    float gyro_rate_y;   // 陀螺仪Y轴角速度
    float gyro_rate_z;   // 陀螺仪Z轴角速度
} Attitude_Data;

// 函数声明
void Attitude_Init(void);
void Kalman_Init(KalmanFilter *kf);
float ComplementaryFilter(float accelAngle, float gyroRate, float dt, float alpha);
float Kalman_Update(KalmanFilter *kf, float newAngle, float newRate, float dt);
HAL_StatusTypeDef Attitude_Update(MPU6050_Data *imu_data, Attitude_Data *attitude);
float Calculate_Accel_Angle_X(MPU6050_Data *data);
float Calculate_Accel_Angle_Y(MPU6050_Data *data);
void Attitude_Calibrate(void);

#endif /* __ATTITUDE_H */
